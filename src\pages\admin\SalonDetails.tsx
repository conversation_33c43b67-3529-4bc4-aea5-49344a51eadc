import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowLeft,
  Building2,
  MapPin,
  Phone,
  Mail,
  Star,
  Users,
  Scissors,
  Calendar,
  DollarSign,
  TrendingUp,
  Clock,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react';

import { SalonService } from '@/services/salonService';
import { ServiceService } from '@/services/serviceService';
import { StaffService } from '@/services/staffService';
import { BookingService } from '@/services/bookingService';
import { toast } from 'sonner';
import { InlineLoader } from '@/components/ui/loading-spinner';
import { Salon, Service, Staff, Booking } from '@/types';

const SalonDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [salon, setSalon] = useState<Salon | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSalonData = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Load salon data
        const [salonData, servicesData, staffData, bookingsData] = await Promise.all([
          SalonService.getSalonById(id),
          ServiceService.getServicesBySalon(id),
          StaffService.getStaffBySalon(id),
          BookingService.getBookingsBySalon(id)
        ]);

        setSalon(salonData);
        setServices(servicesData);
        setStaff(staffData);
        setBookings(bookingsData);
      } catch (error) {
        console.error('Error loading salon data:', error);
        toast.error('Failed to load salon data');
      } finally {
        setLoading(false);
      }
    };

    loadSalonData();
  }, [id]);

  if (loading) {
    return <InlineLoader text="Loading salon details..." />;
  }

  if (!salon) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-glamspot-neutral-900">Salon not found</h2>
          <p className="text-glamspot-neutral-600 mt-2">The salon you're looking for doesn't exist.</p>
          <Button onClick={() => navigate('/admin/salons')} className="mt-4">
            Back to Salons
          </Button>
        </div>
      </div>
    );
  }

  // Calculate statistics
  const totalRevenue = bookings
    .filter(b => b.status === 'completed')
    .reduce((sum, b) => sum + b.totalAmount, 0);

  const averageRating = salon.rating;
  const totalBookings = bookings.length;
  const activeServices = services.filter(s => s.isActive).length;
  const activeStaff = staff.filter(s => s.isActive).length;

  const recentBookings = bookings
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => navigate('/admin/salons')}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Salons
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">{salon.name}</h1>
          <p className="text-glamspot-neutral-600 mt-1">{salon.description}</p>
        </div>
        <Badge variant={salon.isActive ? 'default' : 'secondary'} 
               className={salon.isActive ? 'bg-green-100 text-green-800' : ''}>
          {salon.isActive ? 'Active' : 'Inactive'}
        </Badge>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Revenue
            </CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">Tsh {totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-green-600 mt-1">
              From {bookings.filter(b => b.status === 'completed').length} completed bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Rating
            </CardTitle>
            <Star className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{averageRating}</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              {salon.reviews} reviews
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Total Bookings
            </CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{totalBookings}</div>
            <p className="text-xs text-blue-600 mt-1">
              {bookings.filter(b => b.status === 'pending').length} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Services
            </CardTitle>
            <Scissors className="h-4 w-4 text-glamspot-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{activeServices}</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              {services.length} total services
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-glamspot-neutral-600">
              Staff
            </CardTitle>
            <Users className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-glamspot-neutral-900">{activeStaff}</div>
            <p className="text-xs text-glamspot-neutral-500 mt-1">
              {staff.length} total staff
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="staff">Staff</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Salon Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5 text-glamspot-primary" />
                  Salon Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-glamspot-neutral-500" />
                  <span className="text-sm">{salon.address}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-glamspot-neutral-500" />
                  <span className="text-sm">Contact information not available</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-glamspot-neutral-500" />
                  <span className="text-sm">Email not available</span>
                </div>
                <div className="pt-2">
                  <p className="text-sm text-glamspot-neutral-600">{salon.description}</p>
                </div>
              </CardContent>
            </Card>

            {/* Recent Bookings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-glamspot-primary" />
                  Recent Bookings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentBookings.length > 0 ? (
                    recentBookings.map((booking) => (
                      <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{booking.customerName}</p>
                          <p className="text-xs text-glamspot-neutral-500">
                            {booking.date} at {booking.time}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className={getStatusColor(booking.status)}>
                            {booking.status}
                          </Badge>
                          <p className="text-xs text-glamspot-neutral-500 mt-1">
                            Tsh {booking.totalAmount}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-glamspot-neutral-500 text-center py-4">
                      No bookings found
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scissors className="w-5 h-5 text-glamspot-primary" />
                Services ({services.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services.map((service) => (
                    <TableRow key={service.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{service.name}</p>
                          {service.description && (
                            <p className="text-sm text-glamspot-neutral-500">{service.description}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{service.category}</Badge>
                      </TableCell>
                      <TableCell>Tsh {service.price}</TableCell>
                      <TableCell>{service.duration} min</TableCell>
                      <TableCell>
                        <Badge variant={service.isActive ? 'default' : 'secondary'} 
                               className={service.isActive ? 'bg-green-100 text-green-800' : ''}>
                          {service.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5 text-glamspot-primary" />
                Staff Members ({staff.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Specialty</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {staff.map((staffMember) => (
                    <TableRow key={staffMember.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{staffMember.name}</p>
                          {staffMember.bio && (
                            <p className="text-sm text-glamspot-neutral-500">{staffMember.bio}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{staffMember.specialty}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {staffMember.email && <p>{staffMember.email}</p>}
                          {staffMember.phone && <p>{staffMember.phone}</p>}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={staffMember.isActive ? 'default' : 'secondary'}
                               className={staffMember.isActive ? 'bg-green-100 text-green-800' : ''}>
                          {staffMember.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-glamspot-primary" />
                All Bookings ({bookings.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{booking.customerName}</p>
                          <p className="text-sm text-glamspot-neutral-500">{booking.customerEmail}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{booking.date}</p>
                          <p className="text-sm text-glamspot-neutral-500">{booking.time}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {mockServices.find(s => s.id === booking.serviceId)?.name || 'Unknown Service'}
                      </TableCell>
                      <TableCell>Tsh {booking.totalAmount}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={getStatusColor(booking.status)}>
                          {booking.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SalonDetails;
